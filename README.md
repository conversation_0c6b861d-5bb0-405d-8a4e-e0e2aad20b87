# Market Professors - Financial News & Analysis Platform

A comprehensive financial news and analysis website similar to FXStreet, built with Next.js, TypeScript, and Tailwind CSS.

## 🚀 Features

### Core Features
- **Real-time Market Data**: Live forex rates, cryptocurrency prices, and commodity charts
- **Economic Calendar**: Comprehensive economic events with impact levels and forecasts
- **Financial News**: Latest breaking news with categorization and search functionality
- **Technical Analysis**: Trading tools including pivot points, Fibonacci retracements, and indicators
- **Broker Reviews**: Comprehensive reviews and comparisons of leading forex brokers
- **Cryptocurrency Coverage**: Dedicated crypto news, analysis, and price charts

### Technical Features
- **Modern UI/UX**: Beautiful, responsive design with smooth animations
- **SEO Optimized**: Meta tags, structured data, and semantic HTML
- **Performance**: Fast loading with Next.js optimization
- **Mobile Responsive**: Works perfectly on all devices
- **TypeScript**: Full type safety and better development experience
- **Tailwind CSS**: Utility-first CSS framework for rapid development

## 📁 Project Structure

```
marketprofessors/
├── app/                    # Next.js 13+ app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── rates-charts/      # Charts and rates pages
│   ├── economic-calendar/ # Economic calendar pages
│   ├── news/              # News pages
│   ├── analysis/          # Analysis pages
│   ├── brokers/           # Broker pages
│   ├── cryptocurrencies/  # Crypto pages
│   └── info/              # Information pages
├── components/            # Reusable components
│   ├── layout/           # Header, Footer, etc.
│   └── home/             # Homepage components
├── lib/                  # Utility functions
├── types/                # TypeScript type definitions
├── public/               # Static assets
└── package.json          # Dependencies and scripts
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd marketprofessors
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```

## 📄 Available Pages

### Main Pages
- `/` - Homepage with market overview
- `/rates-charts` - Live charts and market data
- `/economic-calendar` - Economic events calendar
- `/news` - Financial news and analysis
- `/analysis` - Market analysis and insights
- `/brokers` - Broker reviews and comparisons
- `/cryptocurrencies` - Cryptocurrency coverage

### Chart Pages
- `/rates-charts/rates/majors` - Major forex pairs
- `/rates-charts/rates/commodities` - Commodity charts
- `/rates-charts/rates/indices` - Stock indices
- `/rates-charts/rates/crosses` - Cross currency pairs
- `/rates-charts/chart` - Interactive charts
- `/rates-charts/forecast` - Market forecasts

### Economic Calendar Pages
- `/economic-calendar/country/[country]` - Country-specific events
- `/economic-calendar/world-interest-rates` - Global interest rates
- `/economic-calendar/forex-market-hours` - Trading hours
- `/economic-calendar/event/[event]` - Event details

### News & Analysis Pages
- `/news/latest` - Latest news
- `/news/feed` - News RSS feed
- `/analysis/latest` - Latest analysis
- `/analysis/feed` - Analysis RSS feed

### Broker Pages
- `/brokers/[broker-name]` - Individual broker reviews
- `/brokers/broker-news` - Broker news
- `/brokers/pepperstone` - Pepperstone review
- `/brokers/libertex` - Libertex review
- And many more...

### Cryptocurrency Pages
- `/cryptocurrencies` - Crypto overview
- `/cryptocurrencies/news` - Crypto news
- `/cryptocurrencies/rates-chart` - Crypto charts
- `/cryptocurrencies/[coin]` - Individual coin pages

### Information Pages
- `/info/contact-us` - Contact information
- `/info/terms-conditions` - Terms and conditions
- `/info/sitemap` - Site map
- `/info/gdpr-cookies` - Privacy policy
- `/info/prevention` - Risk prevention

### Technical Analysis Pages
- `/technical-analysis/support-resistance/pivot-points` - Pivot points calculator
- `/technical-analysis/support-resistance/Fibonacci` - Fibonacci retracements
- `/rates-charts/indicators` - Technical indicators
- `/rates-charts/indicators/technical-levels` - Technical levels

### Central Bank Pages
- `/macroeconomics/central-banks` - Central banks overview
- `/macroeconomics/central-banks/fed` - Federal Reserve
- `/macroeconomics/central-banks/ecb` - European Central Bank
- `/macroeconomics/central-banks/boe` - Bank of England
- And more central banks...

## 🎨 Customization

### Styling
The project uses Tailwind CSS for styling. You can customize:
- Colors in `tailwind.config.js`
- Global styles in `app/globals.css`
- Component-specific styles in individual components

### Content
- Update market data in `components/home/<USER>
- Modify news articles in `app/news/page.tsx`
- Change economic events in `app/economic-calendar/page.tsx`

### Configuration
- Update site metadata in `app/layout.tsx`
- Modify navigation in `components/layout/Header.tsx`
- Update footer links in `components/layout/Footer.tsx`

## 🔧 Development

### Adding New Pages
1. Create a new directory in `app/` for your route
2. Add a `page.tsx` file with your component
3. Update navigation if needed

### Adding New Components
1. Create a new file in `components/`
2. Export your component
3. Import and use in your pages

### Styling Guidelines
- Use Tailwind CSS classes for styling
- Follow the existing color scheme (primary-600, gray-900, etc.)
- Use the card class for consistent containers
- Maintain responsive design principles

## 📱 Responsive Design

The website is fully responsive and works on:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🚀 Performance

- Optimized images and assets
- Lazy loading for components
- Efficient bundle splitting
- SEO-friendly meta tags
- Fast page transitions

## 🔒 Security

- Input validation
- XSS protection
- Secure headers
- HTTPS ready

## 📈 SEO Features

- Semantic HTML structure
- Meta tags and descriptions
- Open Graph tags
- Twitter Card support
- Sitemap generation
- Structured data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🔄 Updates

Stay updated with the latest changes:
- Follow the repository
- Check the releases page
- Read the changelog

---

**Market Professors** - Your Gateway to Financial Markets 